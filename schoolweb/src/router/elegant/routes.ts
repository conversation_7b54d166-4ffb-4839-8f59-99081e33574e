/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'logmanagement',
    path: '/logmanagement',
    component: 'layout.base$view.logmanagement',
    meta: {
      title: 'logmanagement',
      i18nKey: 'route.logmanagement',
      icon: 'mdi:text-box-search-outline',
      order: 4,
      roles: ['superAdmin']
    }
  },
  {
    name: 'order',
    path: '/order',
    component: 'layout.base$view.order',
    meta: {
      title: 'order',
      i18nKey: 'route.order',
      icon: 'mdi:clipboard-list-outline'
    }
  },
  {
    name: 'profile',
    path: '/profile',
    component: 'layout.base$view.profile',
    meta: {
      title: '个人中心',
      i18nKey: 'route.profile',
      hideInMenu: true,
      icon: 'mdi:account'
    }
  },
  {
    name: 'rolemanagement',
    path: '/rolemanagement',
    component: 'layout.base$view.rolemanagement',
    meta: {
      title: 'rolemanagement',
      i18nKey: 'route.rolemanagement',
      icon: 'mdi:shield-account-outline',
      order: 3,
      roles: ['superAdmin']
    }
  },
  {
    name: 'skill',
    path: '/skill',
    component: 'layout.base$view.skill',
    meta: {
      title: 'skill',
      i18nKey: 'route.skill',
      icon: 'mdi:school-outline'
    }
  },
  {
    name: 'skill-detail',
    path: '/skill-detail',
    component: 'layout.base$view.skill-detail',
    meta: {
      title: 'skill-detail',
      i18nKey: 'route.skill-detail',
      icon: 'mdi:book-open-page-variant-outline',
      hideInMenu: true
    }
  },
  {
    name: 'skill-publish',
    path: '/skill-publish',
    component: 'layout.base$view.skill-publish',
    meta: {
      title: 'skill-publish',
      i18nKey: 'route.skill-publish',
      icon: 'mdi:plus-circle-outline'
    }
  },
  {
    name: 'social',
    path: '/social',
    component: 'layout.base$view.social',
    meta: {
      title: '校园动态',
      i18nKey: 'route.social',
      icon: 'mdi:account-group-outline',
      order: 3
    }
  },
  {
    name: 'system-management',
    path: '/system-management',
    component: 'layout.base$view.system-management',
    meta: {
      title: '系统管理',
      i18nKey: 'route.system-management',
      icon: 'mdi:cog',
      order: 5,
      roles: ['superAdmin']
    }
  },
  {
    name: 'user-credit',
    path: '/user-credit',
    component: 'layout.base$view.user-credit',
    meta: {
      title: 'user-credit',
      i18nKey: 'route.user-credit',
      icon: 'mdi:star-circle-outline',
      roles: ['superAdmin']
    }
  },
  {
    name: 'usermanagement',
    path: '/usermanagement',
    component: 'layout.base$view.usermanagement',
    meta: {
      title: 'usermanagement',
      i18nKey: 'route.usermanagement',
      icon: 'mdi:account-cog-outline',
      order: 3,
      roles: ['superAdmin']
    }
  },
  {
    name: 'wallet',
    path: '/wallet',
    component: 'layout.base$view.wallet',
    meta: {
      title: 'wallet',
      i18nKey: 'route.wallet',
      icon: 'mdi:wallet-outline'
    }
  }
];
