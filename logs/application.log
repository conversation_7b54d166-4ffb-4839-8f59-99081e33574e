2025-06-05 09:07:23 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=9h17m55s853ms836µs500ns).
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"4mjhIdMJTowkUQpJX8OUm", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"lNET989BQFIPiZ9eNZRVm", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"lNET989BQFIPiZ9eNZRVm","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:07:47.343691300(LocalDateTime), 173(Long)
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"4mjhIdMJTowkUQpJX8OUm","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:07:47.343691300(LocalDateTime), 179(Long)
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:07:47 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"4mjhIdMJTowkUQpJX8OUm", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:07:47 [http-nio-8081-exec-6] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"lNET989BQFIPiZ9eNZRVm", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [POST /admin/backup/create, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"0", pragma:"no-cache", cache-control:"no-cache", x-request-id:"MC7kRK5MD4m1-0Hw1mxPu", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - POST "/admin/backup/create", parameters={}
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.admin.AdminController#createSystemBackup()
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.admin.AdminController.createSystemBackup
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:08:09 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:08:09 [http-nio-8081-exec-5] INFO  c.s.s.impl.SystemBackupServiceImpl - 开始创建系统备份...
2025-06-05 09:08:16 [http-nio-8081-exec-5] INFO  c.s.s.impl.SystemBackupServiceImpl - 数据库导出完成: /files/backup/e9ac2a4a4a3347619ac4ff1710c7799020250605090816.sql
2025-06-05 09:08:31 [http-nio-8081-exec-5] INFO  c.s.s.impl.SystemBackupServiceImpl - 文件打包完成: /files/backup/f3e52c84564a4ccb957c0547fb38149a20250605090831.gz
2025-06-05 09:08:36 [http-nio-8081-exec-5] INFO  c.s.s.impl.SystemBackupServiceImpl - 系统备份创建完成: /files/backup/fcec6d8a0d3740f78f4801d7e1e8cc7220250605090836.zip
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.admin.AdminController.createSystemBackup, 返回结果: {"code":200,"msg":"系统备份创建成功","data":"/files/backup/fcec6d8a0d3740f78f4801d7e1e8cc7220250605090836.zip"}
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 系统管理(String), 备份(String), 创建系统备份(String), /admin/backup/create(String), POST(String), [](String), {"x-request-id":"MC7kRK5MD4m1-0Hw1mxPu","sec-fetch-mode":"cors","content-length":"0","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 系统备份创建成功(String), "/files/backup/fcec6d8a0d3740f78f4801d7e1e8cc7220250605090836.zip"(String), 2(Long), 管理员(String), 2025-06-05T09:08:36.428568800(LocalDateTime), 27095(Long)
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "系统备份创建成功", "data": "/files/backup/fcec6d8a0d3740f78f4801d7e1e8cc72202506050908 (truncated)...]
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:08:36 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: POST /admin/backup/create, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"0", pragma:"no-cache", cache-control:"no-cache", x-request-id:"MC7kRK5MD4m1-0Hw1mxPu", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"2UjCFxLyXmE0ihzu1FCmt", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"2UjCFxLyXmE0ihzu1FCmt","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:08:57.476357900(LocalDateTime), 14(Long)
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:08:57 [http-nio-8081-exec-10] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"2UjCFxLyXmE0ihzu1FCmt", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"sSOxW-GsfWxT7QfzIQAvx", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"sSOxW-GsfWxT7QfzIQAvx","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:08:57.535213600(LocalDateTime), 18(Long)
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:08:57 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"sSOxW-GsfWxT7QfzIQAvx", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs/operation-types, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"HIR2z6Kr-bUnCb2haiSUd", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs/operation-types", parameters={}
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getOperationTypes()
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs?page=1&pageSize=25&startTime=2025-06-04%2016%3A00%3A00&endTime=2025-06-05%2015%3A59%3A59&userId=&username=&operationType=&operationModule=&responseCode=, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"8fZvXzMKoHcKWCDK7vR5-", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs?page=1&pageSize=25&startTime=2025-06-04%2016%3A00%3A00&endTime=2025-06-05%2015%3A59%3A59&userId=&username=&operationType=&operationModule=&responseCode=", parameters={masked}
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getSystemLogs(Integer, Integer, String, String, String, String, String, String, String)
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log WHERE (operation_time BETWEEN ? AND ?) ORDER BY operation_time DESC
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 2025-06-04T16:00(LocalDateTime), 2025-06-05T15:59:59(LocalDateTime)
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 697
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": com.baomidou.mybatisplus.extension.plugins.pagination.Page@5bc454 (truncated)...]
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:08:58 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs?page=1&pageSize=25&startTime=2025-06-04%2016%3A00%3A00&endTime=2025-06-05%2015%3A59%3A59&userId=&username=&operationType=&operationModule=&responseCode=, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"8fZvXzMKoHcKWCDK7vR5-", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 2490
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": [注册, 发送验证码, 验证码登录, 获取用户信息, 登出, 用户数据更新, 身份验证, 登录, 查询, 获取角色类型, 用户列表 (truncated)...]
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:08:58 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs/operation-types, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"HIR2z6Kr-bUnCb2haiSUd", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs/operation-modules, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"lbj6N5qYaBg34nSKV3NOA", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs/operation-modules", parameters={}
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getOperationModules()
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log
2025-06-05 09:08:58 [http-nio-8081-exec-8] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 
2025-06-05 09:08:59 [http-nio-8081-exec-8] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 2490
2025-06-05 09:08:59 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:08:59 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": [用户管理, 角色管理, 积分, 社交, 钱包管理, 管理, 技能管理, 订单管理, 社交管理, 信用评级管理, 系统管理]}]
2025-06-05 09:08:59 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:08:59 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs/operation-modules, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"lbj6N5qYaBg34nSKV3NOA", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs/response-codes, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"zj41z0J70kdgCpwEZt_qT", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs/response-codes", parameters={}
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getResponseCodes()
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 2490
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": [200, 500]}]
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:09:08 [http-nio-8081-exec-1] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs/response-codes, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"zj41z0J70kdgCpwEZt_qT", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"e3Ux_hLBVSyQWdTeKsw3S", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"4NNP72DMV7QjW_RvZSzfp", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"e3Ux_hLBVSyQWdTeKsw3S","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:10:22.625047800(LocalDateTime), 13(Long)
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"4NNP72DMV7QjW_RvZSzfp","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:10:22.637529300(LocalDateTime), 14(Long)
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:10:22 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"e3Ux_hLBVSyQWdTeKsw3S", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:10:22 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", x-request-id:"4NNP72DMV7QjW_RvZSzfp", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:05 [main] INFO  com.school.Main - Starting Main using Java 21.0.4 with PID 67044 (E:\AllCode\project\schoolskill\target\classes started by Dong in E:\AllCode\project\schoolskill)
2025-06-05 09:19:05 [main] DEBUG com.school.Main - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 09:19:05 [main] INFO  com.school.Main - The following 1 profile is active: "dev"
2025-06-05 09:19:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:19:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 09:19:06 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.
2025-06-05 09:19:07 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-06-05 09:19:07 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 09:19:07 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 09:19:07 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 09:19:07 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1613 ms
2025-06-05 09:19:07 [main] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Filter 'requestLoggingFilter' configured for use
2025-06-05 09:19:07 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.school.entity.Permissions".
2025-06-05 09:19:07 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.school.entity.Permissions ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-05 09:19:07 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.school.mapper.RolesDao.getall] is ignored, because it exists, maybe from xml file
2025-06-05 09:19:07 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.school.mapper.SkillServiceMapper.incrementViewCount] is ignored, because it exists, maybe from xml file
2025-06-05 09:19:08 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 96 mappings in 'requestMappingHandlerMapping'
2025-06-05 09:19:08 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /files/**] in 'resourceHandlerMapping'
2025-06-05 09:19:08 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 09:19:08 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 09:19:09 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-06-05 09:19:09 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-06-05 09:19:09 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-06-05 09:19:11 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-05 09:19:11 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-06-05 09:19:15 [main] INFO  com.school.Main - Starting Main using Java 21.0.4 with PID 55776 (E:\AllCode\project\schoolskill\target\classes started by Dong in E:\AllCode\project\schoolskill)
2025-06-05 09:19:15 [main] DEBUG com.school.Main - Running with Spring Boot v3.2.0, Spring v6.1.1
2025-06-05 09:19:15 [main] INFO  com.school.Main - The following 1 profile is active: "dev"
2025-06-05 09:19:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 09:19:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 09:19:16 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24 ms. Found 0 Redis repository interfaces.
2025-06-05 09:19:16 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8081 (http)
2025-06-05 09:19:16 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-05 09:19:16 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.16]
2025-06-05 09:19:17 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-05 09:19:17 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1772 ms
2025-06-05 09:19:17 [main] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Filter 'requestLoggingFilter' configured for use
2025-06-05 09:19:17 [main] WARN  c.b.m.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.school.entity.Permissions".
2025-06-05 09:19:17 [main] WARN  c.b.m.c.injector.DefaultSqlInjector - class com.school.entity.Permissions ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-05 09:19:17 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.school.mapper.RolesDao.getall] is ignored, because it exists, maybe from xml file
2025-06-05 09:19:17 [main] ERROR c.b.m.core.MybatisConfiguration - mapper[com.school.mapper.SkillServiceMapper.incrementViewCount] is ignored, because it exists, maybe from xml file
2025-06-05 09:19:18 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - 96 mappings in 'requestMappingHandlerMapping'
2025-06-05 09:19:18 [main] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Patterns [/webjars/**, /**, /files/**] in 'resourceHandlerMapping'
2025-06-05 09:19:18 [main] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerAdapter - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
2025-06-05 09:19:18 [main] DEBUG o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice
2025-06-05 09:19:19 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8081 (http) with context path ''
2025-06-05 09:19:19 [main] INFO  com.school.Main - Started Main in 4.383 seconds (process running for 5.039)
2025-06-05 09:19:19 [http-nio-8081-exec-7] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 09:19:19 [http-nio-8081-exec-7] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Detected StandardServletMultipartResolver
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Detected AcceptHeaderLocaleResolver
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Detected FixedThemeResolver
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@23993ab2
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Detected org.springframework.web.servlet.support.SessionFlashMapManager@150ed9f1
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
2025-06-05 09:19:19 [http-nio-8081-exec-7] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"OJiGVZwceryCm9PT_DBwp", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:19:19 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:19:19 [http-nio-8081-exec-7] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-06-05 09:19:20 [http-nio-8081-exec-7] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3209b123
2025-06-05 09:19:20 [http-nio-8081-exec-7] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"OJiGVZwceryCm9PT_DBwp","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:19:20.291128700(LocalDateTime), 525(Long)
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:19:20 [http-nio-8081-exec-7] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"OJiGVZwceryCm9PT_DBwp", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"TfTnmRM3THXQOdsy6B0Nh", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - GET "/user/info", parameters={}
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.user.UserController#getUserInfo()
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.user.UserController.getUserInfo
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.user.UserController.getUserInfo, 返回结果: {"code":200,"msg":"ok","data":{"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}}
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 用户管理(String), 获取用户信息(String), 获取当前用户信息(String), /user/info(String), GET(String), [](String), {"x-request-id":"TfTnmRM3THXQOdsy6B0Nh","sec-fetch-mode":"cors","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), ok(String), {"userId":2,"name":"管理员","stuId":null,"academy":null,"email":"<EMAIL>","avatar":null,"cardPhoto":null,"cardId":null,"roles":["superAdmin"],"isfirstlogin":false}(String), 2(Long), 管理员(String), 2025-06-05T09:19:20.418578300(LocalDateTime), 22(Long)
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": UserInfoVo(userId=2, name=管理员, stuId=null, academy=null, email=33 (truncated)...]
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:19:20 [http-nio-8081-exec-1] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /user/info, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"TfTnmRM3THXQOdsy6B0Nh", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs/operation-types, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"5oGb5SvKyddwTenVHy8L5", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs/operation-types", parameters={}
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getOperationTypes()
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs?page=1&pageSize=25&startTime=2025-06-04%2016%3A00%3A00&endTime=2025-06-05%2015%3A59%3A59&userId=&username=&operationType=&operationModule=&responseCode=, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"BJnvymAaPSNviZlLCPylv", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs?page=1&pageSize=25&startTime=2025-06-04%2016%3A00%3A00&endTime=2025-06-05%2015%3A59%3A59&userId=&username=&operationType=&operationModule=&responseCode=", parameters={masked}
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getSystemLogs(Integer, Integer, String, String, String, String, String, String, String)
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log WHERE (operation_time BETWEEN ? AND ?) ORDER BY operation_time DESC
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 2025-06-04T16:00(LocalDateTime), 2025-06-05T15:59:59(LocalDateTime)
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 701
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": com.baomidou.mybatisplus.extension.plugins.pagination.Page@31e65b (truncated)...]
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:19:21 [http-nio-8081-exec-4] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs?page=1&pageSize=25&startTime=2025-06-04%2016%3A00%3A00&endTime=2025-06-05%2015%3A59%3A59&userId=&username=&operationType=&operationModule=&responseCode=, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"BJnvymAaPSNviZlLCPylv", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 2494
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": [注册, 发送验证码, 验证码登录, 获取用户信息, 登出, 用户数据更新, 身份验证, 登录, 查询, 获取角色类型, 用户列表 (truncated)...]
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:19:21 [http-nio-8081-exec-3] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs/operation-types, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"5oGb5SvKyddwTenVHy8L5", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs/operation-modules, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"TZlmI3AT5GKMUks6_W-lH", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs/operation-modules", parameters={}
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getOperationModules()
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 2494
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": [用户管理, 角色管理, 积分, 社交, 钱包管理, 管理, 技能管理, 订单管理, 社交管理, 信用评级管理, 系统管理]}]
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:19:21 [http-nio-8081-exec-5] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs/operation-modules, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"TZlmI3AT5GKMUks6_W-lH", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [POST /admin/backup/create, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"0", pragma:"no-cache", cache-control:"no-cache", x-request-id:"kyu70U9U5TQ6fpmkkwTNp", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - POST "/admin/backup/create", parameters={}
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.admin.AdminController#createSystemBackup()
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 开始执行方法: com.school.controller.admin.AdminController.createSystemBackup
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法参数: []
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==>  Preparing: SELECT user_id,avatar,name,stu_id,academy,card_id,card_photo,user_type,status,status_reason,status_update_time,email,phone,password,credit_score,credit_level,bio,skills,follower_count,following_count,is_first_login,last_login_time,created_time,updated_time FROM users WHERE user_id=?
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - ==> Parameters: 2(Integer)
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG c.s.mapper.UserMapper.selectById - <==      Total: 1
2025-06-05 09:19:30 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 当前登录用户: 管理员, ID: 2
2025-06-05 09:19:30 [http-nio-8081-exec-2] INFO  c.s.s.impl.SystemBackupServiceImpl - 开始创建系统备份...
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /logs/response-codes, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"bSKx0RAPEYABIhL6N_VYn", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - GET "/logs/response-codes", parameters={}
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestMappingHandlerMapping - Mapped to com.school.controller.SysLogController#getResponseCodes()
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - ==>  Preparing: SELECT r.role_name FROM roles r INNER JOIN user_roles ur ON r.role_id = ur.role_id WHERE ur.user_id = ?
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - ==> Parameters: 2(String)
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG com.school.mapper.RolesDao.getall - <==      Total: 1
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG c.s.mapper.SysLogMapper.selectList - ==>  Preparing: SELECT id,operation_module,operation_type,operation_desc,request_url,request_method,request_params,request_headers,operation_ip,response_code,response_msg,response_data,user_id,username,operation_time,execution_time FROM sys_log
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG c.s.mapper.SysLogMapper.selectList - ==> Parameters: 
2025-06-05 09:19:30 [http-nio-8081-exec-8] DEBUG c.s.mapper.SysLogMapper.selectList - <==      Total: 2494
2025-06-05 09:19:31 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:19:31 [http-nio-8081-exec-8] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "ok", "data": [200, 500]}]
2025-06-05 09:19:31 [http-nio-8081-exec-8] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:19:31 [http-nio-8081-exec-8] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /logs/response-codes, headers=[host:"127.0.0.1:8081", connection:"keep-alive", x-request-id:"bSKx0RAPEYABIhL6N_VYn", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:19:35 [http-nio-8081-exec-2] INFO  c.s.s.impl.SystemBackupServiceImpl - 数据库导出完成: /files/backup/20250605_091935.sql
2025-06-05 09:20:00 [http-nio-8081-exec-2] INFO  c.s.s.impl.SystemBackupServiceImpl - 文件打包完成: /files/backup/20250605_092000.gz
2025-06-05 09:20:13 [http-nio-8081-exec-2] INFO  c.s.s.impl.SystemBackupServiceImpl - 系统备份创建完成: /files/backup/20250605_092013.zip
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 方法执行完成: com.school.controller.admin.AdminController.createSystemBackup, 返回结果: {"code":200,"msg":"系统备份创建成功","data":"/files/backup/20250605_092013.zip"}
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==>  Preparing: INSERT INTO sys_log ( operation_module, operation_type, operation_desc, request_url, request_method, request_params, request_headers, operation_ip, response_code, response_msg, response_data, user_id, username, operation_time, execution_time ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - ==> Parameters: 系统管理(String), 备份(String), 创建系统备份(String), /admin/backup/create(String), POST(String), [](String), {"x-request-id":"kyu70U9U5TQ6fpmkkwTNp","sec-fetch-mode":"cors","content-length":"0","referer":"http://localhost:9527/","satoken":"74cd1dd3-449e-42ad-8544-68fc4a422e2b","sec-fetch-site":"cross-site","accept-language":"zh-CN,zh;q=0.9,en;q=0.8","x-browser-fingerprint":"097878b9fadab8eb88e96bdb8528254b","origin":"http://localhost:9527","pragma":"no-cache","accept":"application/json, text/plain, */*","sec-ch-ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A/Brand\";v=\"99\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"Windows\"","host":"127.0.0.1:8081","connection":"keep-alive","cache-control":"no-cache","accept-encoding":"gzip, deflate, br, zstd","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-fetch-dest":"empty"}(String), 127.0.0.1(String), 200(Integer), 系统备份创建成功(String), "/files/backup/20250605_092013.zip"(String), 2(Long), 管理员(String), 2025-06-05T09:20:13.420237(LocalDateTime), 43015(Long)
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG c.school.mapper.SysLogMapper.insert - <==    Updates: 1
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG c.school.aspect.OperationLogAspect - 操作日志保存成功
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor - Writing [{"code": 200, "msg": "系统备份创建成功", "data": "/files/backup/20250605_092013.zip"}]
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:20:13 [http-nio-8081-exec-2] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: POST /admin/backup/create, headers=[host:"127.0.0.1:8081", connection:"keep-alive", content-length:"0", pragma:"no-cache", cache-control:"no-cache", x-request-id:"kyu70U9U5TQ6fpmkkwTNp", sec-ch-ua-platform:""Windows"", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", satoken:"74cd1dd3-449e-42ad-8544-68fc4a422e2b", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"application/json, text/plain, */*", x-browser-fingerprint:"097878b9fadab8eb88e96bdb8528254b", origin:"http://localhost:9527", sec-fetch-site:"cross-site", sec-fetch-mode:"cors", sec-fetch-dest:"empty", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8"]]
2025-06-05 09:20:19 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Before request [GET /files/backup/20250605_092013.zip, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", sec-ch-ua-platform:""Windows"", upgrade-insecure-requests:"1", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", sec-fetch-site:"cross-site", sec-fetch-mode:"navigate", sec-fetch-user:"?1", sec-fetch-dest:"document", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", cookie:"agh_session=d8386b3b9423d0e75506e51d30b3a7e7; csrftoken=2GlyhZ7qrmQc2PXyRnHMqpSNailbIfBp; accessToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE2NDY4Mzg2MjUsInJuU3RyIjoiNURHUVVCZGxSbG5KZUNUb3BSdEZmeE5ZSnZSWG14elEiLCJuYW1lIjoi566h55CGIn0.e0WYATw8Bq3mZywnPhB22nrZvRJls2OczMndumyeFBk"]]
2025-06-05 09:20:19 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - GET "/files/backup/20250605_092013.zip", parameters={}
2025-06-05 09:20:19 [http-nio-8081-exec-9] DEBUG o.s.w.s.h.SimpleUrlHandlerMapping - Mapped to ResourceHttpRequestHandler [URL [file:E:/AllCode/project/schoolskill/upload/]]
2025-06-05 09:20:22 [http-nio-8081-exec-9] DEBUG o.s.web.servlet.DispatcherServlet - Completed 200 OK
2025-06-05 09:20:22 [http-nio-8081-exec-9] DEBUG o.s.w.f.CommonsRequestLoggingFilter - Incoming Request: GET /files/backup/20250605_092013.zip, headers=[host:"127.0.0.1:8081", connection:"keep-alive", pragma:"no-cache", cache-control:"no-cache", sec-ch-ua:""Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"", sec-ch-ua-mobile:"?0", sec-ch-ua-platform:""Windows"", upgrade-insecure-requests:"1", user-agent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", sec-fetch-site:"cross-site", sec-fetch-mode:"navigate", sec-fetch-user:"?1", sec-fetch-dest:"document", referer:"http://localhost:9527/", accept-encoding:"gzip, deflate, br, zstd", accept-language:"zh-CN,zh;q=0.9,en;q=0.8", cookie:"agh_session=d8386b3b9423d0e75506e51d30b3a7e7; csrftoken=2GlyhZ7qrmQc2PXyRnHMqpSNailbIfBp; accessToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjE2NDY4Mzg2MjUsInJuU3RyIjoiNURHUVVCZGxSbG5KZUNUb3BSdEZmeE5ZSnZSWG14elEiLCJuYW1lIjoi566h55CGIn0.e0WYATw8Bq3mZywnPhB22nrZvRJls2OczMndumyeFBk"]]
2025-06-05 09:24:33 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-06-05 09:24:33 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
